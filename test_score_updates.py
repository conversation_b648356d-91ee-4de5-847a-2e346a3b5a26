"""
Test script for verifying the improved score update functionality.

This script tests:
1. First-time task submissions
2. Score improvements on retries
3. Task set score synchronization
4. Score calculation accuracy
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from bson import ObjectId

# Import the classes we want to test
from app.v1.api.management_service.storage import TaskStorage
from app.shared.models.task import QuizType


class TestScoreUpdates:
    """Test the improved score update functionality."""
    
    @pytest.fixture
    def mock_user(self):
        """Create a mock user for testing."""
        user = Mock()
        user.user.id = "test_user_123"
        user.async_db = Mock()
        
        # Mock database collections
        user.async_db.task_items = Mock()
        user.async_db.task_sets = Mock()
        
        return user
    
    @pytest.fixture
    def task_storage(self, mock_user):
        """Create TaskStorage instance with mock user."""
        return TaskStorage(mock_user)
    
    @pytest.mark.asyncio
    async def test_first_submission_score_update(self, task_storage, mock_user):
        """Test score update on first submission."""
        task_id = str(ObjectId())
        task_set_id = ObjectId()
        
        # Mock task item data
        mock_task = {
            "_id": ObjectId(task_id),
            "type": "single_choice",
            "question": {"text": "Test question", "answer": "A"},
            "correct_answer": {"value": "A"},
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "attempts_count": 0
        }
        
        # Mock task set data
        mock_task_set = {
            "_id": task_set_id,
            "tasks": [ObjectId(task_id)],
            "scored": 0,
            "attempted_tasks": 0,
            "total_score": 10
        }
        
        # Setup mocks
        mock_user.async_db.task_items.find_one = AsyncMock(return_value=mock_task)
        mock_user.async_db.task_sets.find_one = AsyncMock(return_value=mock_task_set)
        mock_user.async_db.task_items.update_one = AsyncMock(return_value=Mock(modified_count=1))
        mock_user.async_db.task_sets.update_one = AsyncMock(return_value=Mock(modified_count=1))
        
        # Test first submission
        result = await task_storage.submit_task_item(
            task_id=task_id,
            answer="A",
            task_type=QuizType.SINGLE_CHOICE
        )
        
        # Verify results
        assert result["is_correct"] is True
        assert result["scored"] == 10
        assert result["is_first_attempt"] is True
        
        # Verify task item was updated
        mock_user.async_db.task_items.update_one.assert_called()
        
        # Verify task set was updated
        mock_user.async_db.task_sets.update_one.assert_called()
    
    @pytest.mark.asyncio
    async def test_score_improvement_on_retry(self, task_storage, mock_user):
        """Test score improvement when retrying a task."""
        task_id = str(ObjectId())
        task_set_id = ObjectId()
        
        # Mock task item data (already submitted with lower score)
        mock_task = {
            "_id": ObjectId(task_id),
            "type": "multiple_choice",
            "question": {"text": "Test question", "answer": ["A", "B"]},
            "correct_answer": {"value": ["A", "B"]},
            "total_score": 15,
            "scored": 7,  # Previous partial score
            "submitted": True,
            "attempts_count": 1
        }
        
        # Mock task set data
        mock_task_set = {
            "_id": task_set_id,
            "tasks": [ObjectId(task_id)],
            "scored": 7,  # Current score includes the old score
            "attempted_tasks": 1,
            "total_score": 15
        }
        
        # Setup mocks
        mock_user.async_db.task_items.find_one = AsyncMock(return_value=mock_task)
        mock_user.async_db.task_sets.find_one = AsyncMock(return_value=mock_task_set)
        mock_user.async_db.task_items.update_one = AsyncMock(return_value=Mock(modified_count=1))
        mock_user.async_db.task_sets.update_one = AsyncMock(return_value=Mock(modified_count=1))
        
        # Test retry with correct answer (should get full score)
        result = await task_storage.submit_task_item(
            task_id=task_id,
            answer=["A", "B"],
            task_type=QuizType.MULTIPLE_CHOICE
        )
        
        # Verify results
        assert result["is_correct"] is True
        assert result["scored"] == 15  # Full score
        assert result["is_first_attempt"] is False
        
        # Verify task item was updated with new score
        mock_user.async_db.task_items.update_one.assert_called()
        
        # Verify task set score was updated (should reflect the improvement)
        mock_user.async_db.task_sets.update_one.assert_called()
    
    @pytest.mark.asyncio
    async def test_score_synchronization(self, task_storage, mock_user):
        """Test task set score synchronization."""
        task_set_id = ObjectId()
        task_id_1 = ObjectId()
        task_id_2 = ObjectId()
        
        # Mock task set with inconsistent scores
        mock_task_set = {
            "_id": task_set_id,
            "tasks": [task_id_1, task_id_2],
            "scored": 10,  # Incorrect total
            "attempted_tasks": 1,  # Incorrect count
            "total_score": 20
        }
        
        # Mock task items with actual scores
        mock_task_items = [
            {
                "_id": task_id_1,
                "scored": 8,
                "total_score": 10,
                "submitted": True
            },
            {
                "_id": task_id_2,
                "scored": 7,
                "total_score": 10,
                "submitted": True
            }
        ]
        
        # Setup mocks
        mock_user.async_db.task_sets.find_one = AsyncMock(return_value=mock_task_set)
        mock_user.async_db.task_items.find = Mock()
        mock_user.async_db.task_items.find.return_value.to_list = AsyncMock(return_value=mock_task_items)
        mock_user.async_db.task_sets.update_one = AsyncMock(return_value=Mock(modified_count=1))
        
        # Test synchronization
        result = await task_storage._sync_task_set_scores(task_set_id)
        
        # Verify results
        assert result["success"] is True
        assert len(result["discrepancies"]) > 0  # Should find discrepancies
        assert result["new_scores"]["scored"] == 15  # 8 + 7
        assert result["new_scores"]["attempted_tasks"] == 2
        
        # Verify task set was updated
        mock_user.async_db.task_sets.update_one.assert_called()


def test_score_calculation_logic():
    """Test score calculation for different question types."""
    from app.v1.api.management_service.storage import TaskStorage
    
    # Create a mock storage instance
    storage = TaskStorage(Mock())
    
    # Test single choice scoring
    result = storage._score_single_choice("A", "A", {"A": "Correct", "B": "Wrong"})
    assert result["is_correct"] is True
    assert result["score_earned"] == 10  # ScoreValue.SINGLE_CHOICE
    
    result = storage._score_single_choice("B", "A", {"A": "Correct", "B": "Wrong"})
    assert result["is_correct"] is False
    assert result["score_earned"] == 0
    
    # Test multiple choice scoring with partial credit
    result = storage._score_multiple_choice(["A", "B"], ["A", "B"], {"A": "Option A", "B": "Option B", "C": "Option C"})
    assert result["is_correct"] is True
    assert result["score_earned"] == 15  # ScoreValue.MULTIPLE_CHOICE
    
    # Test partial credit
    result = storage._score_multiple_choice(["A"], ["A", "B"], {"A": "Option A", "B": "Option B", "C": "Option C"})
    assert result["is_correct"] is False
    assert result["score_earned"] == 7  # Partial credit


if __name__ == "__main__":
    """Run tests directly."""
    print("🧪 Running score update tests...")
    
    # Run the synchronous test
    test_score_calculation_logic()
    print("✅ Score calculation tests passed!")
    
    # For async tests, you would typically use pytest
    print("📝 To run async tests, use: pytest test_score_updates.py -v")
